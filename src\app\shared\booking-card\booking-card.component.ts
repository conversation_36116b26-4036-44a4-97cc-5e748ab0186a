import { Component, input, output, signal } from '@angular/core';
import {
  AlertButton,
  IonAccordion,
  IonAccordionGroup,
  IonAlert,
  IonBadge,
  IonButton,
  IonCard,
  IonIcon,
} from '@ionic/angular/standalone';
import { bookingStatusType, IBooking } from '@models/interfaces/booking';
import { paymentMethodType } from '@models/interfaces/payment';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { addIcons } from 'ionicons';
import {
  alertCircleOutline,
  briefcaseOutline,
  calendarOutline,
  cartOutline,
  checkmarkCircle,
  chevronDownOutline,
  chevronUpOutline,
  closeOutline,
  createOutline,
  logoEuro,
  person,
  personOutline,
  pricetagOutline,
  shieldCheckmarkOutline,
  timeOutline,
  timerOutline,
  trashOutline,
  walletOutline,
} from 'ionicons/icons';

@Component({
  selector: 'app-booking-card',
  templateUrl: './booking-card.component.html',
  styleUrls: ['./booking-card.component.scss'],
  standalone: true,
  imports: [
    IonCard,
    IonButton,
    IonBadge,
    IonIcon,
    IonAccordion,
    IonAccordionGroup,
    IonAlert,
  ],
})
export class BookingCardComponent {
  booking = input.required<IBooking>();
  onEdit = output<void>();
  onCancel = output<void>();

  bookingStatusType = bookingStatusType;
  isConfirmModalOpen = signal<boolean>(false);
  isExpanded = signal<boolean>(false);

  paymentMethodType = paymentMethodType;

  protected alertButtons: AlertButton[] = [
    {
      text: 'Annulla',
      role: 'cancel',
      cssClass: 'alert-button-cancel',
      handler: () => {
        this.closeConfirmModal();
      },
    },
    {
      text: 'Conferma',
      role: 'confirm',
      cssClass: 'alert-button-confirm',
      handler: () => {
        this.confirmCancel();
      },
    },
  ];

  constructor() {
    addIcons({
      calendarOutline,
      timeOutline,
      personOutline,
      person,
      timerOutline,
      createOutline,
      alertCircleOutline,
      closeOutline,
      checkmarkCircle,
      chevronDownOutline,
      chevronUpOutline,
      trashOutline,
      logoEuro,
      pricetagOutline,
      cartOutline,
      walletOutline,
      shieldCheckmarkOutline,
      briefcaseOutline,
    });
  }

  /**
   * Gestisce il cambio di stato dell'accordion
   */
  accordionChanged(event: any) {
    // Controlla se l'accordion è aperto (se il valore include 'booking-details')
    const isOpen = event.detail && event.detail.value === 'booking-details';
    this.isExpanded.set(isOpen);
  }

  openConfirmModal() {
    this.isConfirmModalOpen.set(true);
  }

  closeConfirmModal() {
    this.isConfirmModalOpen.set(false);
  }

  confirmCancel() {
    this.closeConfirmModal();
    this.onCancel.emit();
  }

  formatDate(date: string): string {
    // Formatta la data con date-fns
    const formattedDate = format(new Date(date), 'EEEE d MMMM yyyy', {
      locale: it,
    });

    // Capitalizza la prima lettera del giorno
    return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);
  }

  /**
   * Estrae il giorno dalla data
   */
  getDayFromDate(date: string): string {
    return format(new Date(date), 'd', { locale: it });
  }

  /**
   * Estrae il mese dalla data in formato abbreviato e lo capitalizza
   */
  getMonthFromDate(date: string): string {
    const month = format(new Date(date), 'MMM', { locale: it });
    return month.toUpperCase();
  }

  getDayOfWeek(date: string): string {
    const dayOfWeek = format(new Date(date), 'EEEE', { locale: it });
    return dayOfWeek.charAt(0).toUpperCase() + dayOfWeek.slice(1);
  }
}
