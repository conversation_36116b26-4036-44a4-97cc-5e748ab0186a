<ion-card class="booking-card">
  <!-- Header con nome operatore e stato -->
  <div class="card-top-header">
    <div class="staff-info">
      <ion-icon name="briefcase-outline"></ion-icon>
      <span>{{ booking().staff.name }} {{ booking().staff.surname }}</span>
    </div>
    <ion-badge class="status-badge" [class]="booking().status">
      {{
        booking().status === bookingStatusType.booked
          ? "Prenotato"
          : "Completato"
      }}
    </ion-badge>
  </div>

  <!-- Accordion per espandere/collassare i dettagli -->
  <ion-accordion-group
    [multiple]="false"
    (ionChange)="accordionChanged($event)"
  >
    <ion-accordion value="booking-details">
      <!-- Header dell'accordion (sempre visibile) -->
      <div slot="header" class="accordion-header">
        <!-- Box data a sinistra -->
        <div class="date-box">
          <div class="day-week">{{ getDayOfWeek(booking().date) }}</div>
          <div class="day">{{ getDayFromDate(booking().date) }}</div>
          <div class="month">{{ getMonthFromDate(booking().date) }}</div>
        </div>

        <!-- Contenuto centrale -->
        <div class="header-content">
          <div class="time-row">
            <span class="time-label">Ore {{ booking().startTime }}</span>
          </div>

          <!-- Servizi (visibili quando non è espanso) -->
          <div class="services-preview">
            @for (service of booking().services; track service.id) {
              <div class="service-item">
                {{ service.name }}
              </div>
            }
          </div>
        </div>

        <!-- Freccia a destra -->
        <div class="chevron-container">
          <ion-icon
            [name]="
              isExpanded() ? 'chevron-up-outline' : 'chevron-down-outline'
            "
            class="expand-icon"
          ></ion-icon>
        </div>
      </div>

      <!-- Contenuto dell'accordion (visibile solo quando espanso) -->
      <div slot="content" class="accordion-content">
        <!-- Dettagli completi -->
        <div class="details-section">
          <!-- Data completa -->
          <div class="detail-item">
            <div class="detail-icon">
              <ion-icon name="calendar-outline"></ion-icon>
            </div>
            <div class="detail-content">
              <div class="detail-label">DATA</div>
              <div class="detail-value">
                {{ formatDate(booking().date) }} - Ore:
                {{ booking().startTime }}
              </div>
            </div>
          </div>

          <!-- Operatore -->
          <div class="detail-item">
            <div class="detail-icon">
              <ion-icon name="briefcase-outline"></ion-icon>
            </div>
            <div class="detail-content">
              <div class="detail-label">OPERATORE</div>
              <div class="detail-value">
                {{ booking().staff.name }} {{ booking().staff.surname }}
              </div>
            </div>
          </div>

          <!-- Cliente -->
          <div class="detail-item">
            <div class="detail-icon">
              <ion-icon name="person-outline"></ion-icon>
            </div>
            <div class="detail-content">
              <div class="detail-label">CLIENTE</div>
              <div class="detail-value">
                {{ booking().user.name }} {{ booking().user.surname }}
              </div>
            </div>
          </div>

          <!-- Metodo di pagamento -->
          <div class="detail-item">
            <div class="detail-icon">
              <ion-icon name="wallet-outline"></ion-icon>
            </div>
            <div class="detail-content">
              <div class="detail-label">METODO DI PAGAMENTO</div>
              <div class="detail-value">
                @switch ($any(booking().payment).methodType) {
                  @case (paymentMethodType.cash) {
                    In sede
                  }
                  @case (paymentMethodType.card) {
                    Carta di credito
                  }
                  @case (paymentMethodType.paypal) {
                    PayPal
                  }
                  @default {
                    Metodo di pagamento non disponibile
                  }
                }
              </div>
            </div>
          </div>

          <!-- Servizi (sezione completa) -->
          <div class="detail-item">
            <div class="detail-icon">
              <ion-icon name="cart-outline"></ion-icon>
            </div>
            <div class="detail-content">
              <div class="detail-label">SERVIZI</div>
              <div class="detail-value">
                @for (service of booking().services; track service.id) {
                  <div class="service-item">
                    <span>{{ service.name }}</span>
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </ion-accordion>
  </ion-accordion-group>

  <!-- Durata e prezzo -->
  <div class="duration-price-row">
    <div class="duration">
      <ion-icon name="timer-outline"></ion-icon>
      <div class="subtitle">DURATA</div>
      <span class="value">{{ booking().totalDuration }} min</span>
    </div>
    <div class="price">
      <ion-icon name="pricetag-outline"></ion-icon>
      <div class="subtitle">PREZZO</div>
      <span class="value">
        <ion-icon class="euro" name="logo-euro"></ion-icon
        >{{ booking().totalPrice?.toFixed(2) }}</span
      >
    </div>
  </div>

  <!-- Pulsanti -->
  @if (booking().status === bookingStatusType.booked) {
    <div class="action-buttons">
      <ion-button
        class="cancel-button"
        fill="solid"
        (click)="openConfirmModal()"
      >
        <ion-icon name="trash-outline" slot="start"></ion-icon>
        Cancella
      </ion-button>
      <ion-button class="edit-button" fill="solid" (click)="onEdit.emit()">
        <ion-icon name="create-outline" slot="start"></ion-icon>
        Modifica
      </ion-button>
    </div>
  }
</ion-card>

<ion-alert
  [isOpen]="isConfirmModalOpen()"
  header="Sei sicuro di voler cancellare questa prenotazione?"
  [buttons]="alertButtons"
></ion-alert>
