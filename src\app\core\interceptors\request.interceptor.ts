import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { GenericUtils } from '@core/utils/generic';
import { Observable, tap } from 'rxjs';

export function requestInterceptor(
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> {
  const tenantID = localStorage.getItem(GenericUtils.tenantID);

  req = req.clone({
    headers: req.headers.append(GenericUtils.tenantID, tenantID || '')
  });

  return next(req).pipe(
    tap(event => {
      // loadingService.setLoading(true, req.url);
    })
  );
}
