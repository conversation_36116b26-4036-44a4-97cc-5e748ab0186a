import { View<PERSON>Scroller } from '@angular/common';
import {
  Component,
  computed,
  effect,
  ElementRef,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  viewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import { BookingService } from '@core/services/booking.service';
import { PaymentsService } from '@core/services/payment.service';
import { ToastService } from '@core/services/toast.service';
import { CustomValidators } from '@core/validators/custom.validator';
import {
  IonButton,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonList,
  IonRadio,
  IonRadioGroup,
  IonTitle,
  IonToolbar,
  NavController,
} from '@ionic/angular/standalone';
import { IBaseResponse } from '@models/interfaces/base-response';
import { IBooking } from '@models/interfaces/booking';
import {
  IPayment,
  paymentMethodType,
  paymentStatusType,
} from '@models/interfaces/payment';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { BookingSummaryComponent } from '@shared/booking-summary/booking-summary.component';
import { StripeButtonComponent } from '@shared/stripe-button/stripe-button.component';
import { addIcons } from 'ionicons';
import { call, cardOutline, mail, person, walletOutline } from 'ionicons/icons';
import { switchMap } from 'rxjs';
import { StripeComponent } from './stripe/stripe.component';

interface PaymentMethod {
  icon: string;
  name: string;
  selected: boolean;
  method: paymentMethodType;
}
const buttonTitle = {
  [paymentMethodType.cash]: 'Conferma prenotazione',
  [paymentMethodType.card]: 'Vai al pagamento',
};

@Component({
  selector: 'app-step-2-user-details',
  templateUrl: './step-2-user-details.component.html',
  styleUrls: ['./step-2-user-details.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    IonContent,
    IonButton,
    IonIcon,
    IonInput,
    IonItem,
    IonList,
    IonLabel,
    IonRadio,
    IonRadioGroup,
    AppbarComponent,
    BookingSummaryComponent,
    IonToolbar,
    IonHeader,
    IonTitle,
    StripeComponent,
    StripeButtonComponent,
  ],
})
export class Step2UserDetailsComponent implements OnInit, OnDestroy {
  protected userForm!: FormGroup;
  protected authService = inject(AuthService);
  protected currentBooking = this.bookingService.currentBooking;

  protected currentUser = this.authService.user;

  paymentMethodType = paymentMethodType;

  onBookStripeClick = signal<boolean>(false);

  protected isLoading = this.bookingService.isLoading;

  paymentAllowed = this.paymentsService.paymentvalid;

  stripeElement = viewChild<ElementRef<HTMLElement>>('stripe');

  buttonTitle = signal<string>(buttonTitle[paymentMethodType.cash]);

  isStripeReady = signal<boolean>(false);

  onPayClick = signal<boolean>(false);

  paymentIntentParams = signal<string>('');

  stripePayButton = computed(
    () =>
      this.isStripeReady() &&
      this.onBookStripeClick() &&
      this.selectedPayment()?.method === paymentMethodType.card,
  );

  stripeContent = computed(
    () =>
      this.onBookStripeClick() &&
      this.selectedPayment()?.method === paymentMethodType.card,
  );

  protected selectedPayment = computed(() =>
    this.paymentMethods().find((method) => method.selected),
  );
  protected paymentMethodEvaluation = effect(() => {
    // console.log('selectedPayment', this.selectedPayment());
    // console.log('currentBooking in effect', this.currentBooking());
    if (this.selectedPayment()?.method === paymentMethodType.cash) {
      this.buttonTitle.set(buttonTitle[paymentMethodType.cash]);
      this.paymentsService.paymentvalid.set(true);
      this.onStripePayAbort();
    }
    if (this.selectedPayment()?.method === paymentMethodType.card) {
      this.buttonTitle.set(buttonTitle[paymentMethodType.card]);
      this.paymentsService.paymentvalid.set(true);
    }
  });

  constructor(
    private fb: FormBuilder,
    private bookingService: BookingService,
    private navCtrl: NavController,
    private paymentsService: PaymentsService,
    private route: ActivatedRoute,
    private router: Router,
    private toastService: ToastService,
    private vps: ViewportScroller,
  ) {
    trackEvent('step_2_user_details_page');
    this.initForm();
    addIcons({
      mail,
      person,
      call,
      walletOutline,
      cardOutline,
    });

    // Handle query params in constructor if needed immediately
    this.route.queryParams.subscribe((params) => {
      if (params['payment_intent']) {
        this.paymentIntentParams.set(params['payment_intent']);
        this.bookingService.setIsLoading(true);
        const storedBooking = localStorage.getItem('booking');
        // console.log('storedBooking', storedBooking);
        if (storedBooking) {
          const bookingData = JSON.parse(storedBooking);
          this.bookingService.setCurrentBooking(bookingData);
          // Handle the successful payment
          if (storedBooking && params['redirect_status'] === 'succeeded') {
            this.bookingService.setCurrentBooking({
              ...bookingData,
              payment: {
                ...bookingData.payment,
                methodType: paymentMethodType.paypal,
              },
            });
            // console.log('bookingData', bookingData);
            // console.log('currentBookingData', this.currentBooking());
            this.#handleSuccessfulPayment();
          } else {
            this.toastService.showError('Pagamento rifiutato');
            this.router
              .navigate([''])
              .then(() => this.bookingService.setIsLoading(false));
          }
        }
      }
    });
  }
  ngOnDestroy(): void {
    // console.log('ngOnDestroy');
    this.bookingService.setCurrentBooking({
      ...(this.currentBooking()! as IBooking),
      id: undefined,
    });
  }

  paymentMethods = signal<PaymentMethod[]>([
    {
      icon: 'wallet-outline',
      name: 'Pagamento in sede',
      selected: false,
      method: paymentMethodType.cash,
    },
    {
      icon: 'card-outline',
      name: 'Pagamento con carta',
      selected: false,
      method: paymentMethodType.card,
    },
  ]);

  private initForm() {
    // Inizializza il form con i dati dell'utente se disponibili
    this.userForm = this.fb.group({
      name: [
        this.currentUser()?.name || null,
        [Validators.required, Validators.minLength(2)],
      ],
      surname: [
        this.currentUser()?.surname || null,
        [Validators.required, Validators.minLength(2)],
      ],
      email: [
        this.currentUser()?.email || null,
        [Validators.required, Validators.pattern(CustomValidators.emailRegex)],
      ],
      mobilePhone: [
        this.currentUser()?.mobilePhone || null,
        [
          Validators.required,
          Validators.minLength(10),
          Validators.maxLength(10),
        ],
      ],
    });
    this.fillForm();
  }

  private fillForm() {
    this.userForm.patchValue({
      name: 'Giona',
      surname: 'Giona',
      email: '<EMAIL>',
      mobilePhone: '3333333333',
    });
  }

  ngOnInit() {
    // Se l'utente è già autenticato, precompila il form
    this.resetFormWithUserData();
  }

  /**
   * Resetta il form con i dati dell'utente loggato
   */
  resetFormWithUserData() {
    if (this.authService.isLoggedIn()) {
      const user = this.authService.user();
      if (user) {
        // Prima disabilitiamo il form, così i validatori non verranno applicati

        // Poi impostiamo i valori
        this.userForm.patchValue({
          name: user.name || '',
          surname: user.surname || '',
          email: user.email || '',
          mobilePhone: user.mobilePhone || '',
        });
      }
    }
  }

  #handleSuccessfulPayment() {
    // console.log('handleSuccessfulPayment');
    try {
      this.manageBooking();
      // Clean up storage after successful booking
      localStorage.removeItem('booking');
    } catch (error) {
      // console.error('Error handling payment:', error);
      this.toastService.showError(
        'Errore durante la creazione della prenotazione',
      );
      this.bookingService.setIsLoading(false);
    }
  }

  selectPaymentMethod(selectedMethodType: paymentMethodType): void {
    this.paymentMethods.update((prev) =>
      prev.map((method) => ({
        ...method,
        selected: method.method === selectedMethodType,
      })),
    );
  }

  onStripePayFailed() {
    this.bookingService.setIsLoading(false);
    this.onPayClick.set(false);
  }

  onStripePayAbort() {
    this.onBookStripeClick.set(false);
    this.onPayClick.set(false);
  }

  onStripePaySuccess(paymentIntentId: string) {
    this.paymentsService
      .confirmPayment(
        (this.currentBooking()?.payment as IPayment).id,
        paymentIntentId,
      )
      .subscribe({
        next: () => {
          this.router
            .navigate(['booking/success'])
            .then(() => this.bookingService.setIsLoading(false));
        },
      });
  }

  onBookClick() {
    this.bookingService.setIsLoading(true);
    if (!this.currentBooking()?.id) {
      this.bookingService.setPaymentMethod(this.selectedPayment()?.method!);
    }
    this.setUserInfoDetails();
    this.manageBooking();
  }

  private setUserInfoDetails() {
    // Se il form è disabilitato ma l'utente è loggato, consideriamo il form valido
    const isFormValid =
      this.userForm.valid ||
      (this.userForm.disabled && this.authService.isLoggedIn());
    if (isFormValid) {
      // Otteniamo i valori del form, anche se è disabilitato
      const formValues = this.userForm.disabled
        ? this.userForm.getRawValue()
        : this.userForm.value;

      // Salva i dati dell'utente nel servizio di prenotazione
      this.bookingService.setUserDetail({
        id: this.authService.user()?.id || '',
        name: formValues.name,
        surname: formValues.surname,
        email: formValues.email,
        mobilePhone: formValues.mobilePhone,
      });
    }
  }

  private isCardPayment(payment: IPayment) {
    return (
      payment.status === paymentStatusType.pending &&
      payment.methodType === paymentMethodType.card
    );
  }

  private isPaypalPayment(payment: IPayment) {
    return (
      payment.status === paymentStatusType.paid &&
      payment.methodType === paymentMethodType.paypal
    );
  }

  private onBookingSuccess(res: IBaseResponse<IBooking>) {
    this.bookingService.setCurrentBooking(res.data!);
    if (this.isCardPayment(res.data?.payment as IPayment)) {
      this.onBookStripeClick.set(true);
      this.bookingService.setIsLoading(false);
    } else if (this.isPaypalPayment(res.data?.payment as IPayment)) {
      this.onStripePaySuccess(this.paymentIntentParams());
    } else {
      this.router
        .navigate(['booking/success'])
        .then(() => this.bookingService.setIsLoading(false));
    }
  }

  private updatePayment() {
    // console.log(
    //   'updatePayment',
    //   this.selectedPayment()?.method,
    //   (this.currentBooking()?.payment as IPayment).methodType,
    // );
    this.paymentsService
      .updatePayment(
        (this.currentBooking()?.payment as IPayment).id!,
        this.currentBooking()?.id!,
        this.selectedPayment()?.method ??
          (this.currentBooking()?.payment as IPayment).methodType,
      )
      .pipe(
        switchMap(() =>
          this.bookingService.getBookingById(this.currentBooking()!.id!),
        ),
      )
      .subscribe({
        next: (res) => {
          this.onBookingSuccess(res);
        },
      });
  }

  protected manageBooking() {
    this.onBookStripeClick.set(false);
    // console.log('createBooking', this.currentBooking());
    if (this.currentBooking()?.id) {
      this.updatePayment();
    } else {
      this.bookingService
        .createBooking(this.currentBooking() as IBooking)
        .subscribe({
          next: (res) => {
            this.onBookingSuccess(res);
            localStorage.setItem('booking', JSON.stringify(res.data));
          },
          error: () => {
            this.bookingService.setIsLoading(false);
            this.bookingService.setBookingError(true);
            this.toastService.showError(
              'Errore durante la creazione della prenotazione',
            );
          },
        });
    }
  }
}
