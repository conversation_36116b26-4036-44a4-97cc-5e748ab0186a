import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
/** Models */
import { IBaseResponse } from '@models/interfaces/base-response';
import { IPayment, paymentMethodType } from '@models/interfaces/payment';
/** Env */

@Injectable({
  providedIn: 'root',
})
export class PaymentsService {
  paymentvalid = signal(false);
  paymentIntent = signal(false);

  private _basePaymentsApi = environment.api.payments!;
  constructor(private http: HttpClient) {}

  createPayment(userId: string, bookingId: string, idempotencyKey: string) {
    return this.http.post<IBaseResponse<any>>(
      `${this._basePaymentsApi}/create`,
      { userId, bookingId },
      { headers: { clientId: idempotencyKey } },
    );
  }

  confirmPayment(paymentId: string, stripeId: any) {
    return this.http.post<IBaseResponse<void>>(
      `${this._basePaymentsApi}/confirm`,
      { paymentId, stripeId },
    );
  }

  updatePayment(
    paymentId: string,
    bookingId: string,
    methodType: paymentMethodType,
  ) {
    return this.http.put<IBaseResponse<IPayment>>(
      `${this._basePaymentsApi}/${paymentId}`,
      { bookingId, methodType },
    );
  }
}
