import { IPayment } from './payment';
import { IStaff } from './staff';
import { IUser } from './user';

export interface IBooking {
  id?: string;
  user: IUser;
  date: string;
  startTime: string;
  endTime?: string;
  status?: bookingStatusType;
  services: IService[];
  totalPrice?: number;
  payment: IPayment | string;
  totalDuration?: number;
  staff: IStaff;
}

export interface IBookingRequest {
  id?: string;
  user: IUser;
  date: string;
  startTime: string;
  endTime?: string;
  status?: bookingStatusType;
  services: IService['id'][];
  totalPrice?: number;
  payment: IPayment['methodType'];
  totalDuration?: number;
  staff: IStaff['id'];
}

export enum bookingStatusType {
  booked = 'booked',
  completed = 'completed',
}

export interface IService {
  id: string;
  name: string;
  price: number;
  duration: serviceDurationType;
  icon?: serviceIconType;
  imageUrl: string;
}

export enum serviceDurationType {
  m15 = 15,
  m30 = 30,
  m60 = 60,
}

export enum serviceIconType {
  haircut = 'haircut',
  barbcut = 'barbcut',
  hairwash = 'hairwash',
  haircolor = 'haircolor',
}
